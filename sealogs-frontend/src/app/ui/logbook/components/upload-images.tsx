'use client'

import React, { use, useEffect, useRef, useState } from 'react'
import AWS from 'aws-sdk'
import { AlertDialogNew, Button } from '@/components/ui'
import { toast } from '@/hooks/use-toast'
import { Camera } from 'lucide-react'
import { cn } from '../../../../../utils/cn'
import { useSearchParams } from 'next/navigation'
import { useMutation } from '@apollo/client'
import {
    CREATE_SECTION_MEMBER_IMAGE,
    DELETE_SECTION_MEMBER_IMAGE,
} from '@/app/lib/graphQL/mutation'
import UploadCloudFlare from './upload-cf'
import { BaseFile } from '@/components/ui/file-upload-ui'
import { get } from 'lodash'
import { getResponsiveLabel } from '../../../../../utils/responsiveLabel'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'

const ACCOUNT_ID = 'ddde1c1cd1aa25641691808dcbafdeb7'
const ACCESS_KEY_ID = '06c3e13a539f24e6fdf7075bf381bf5e'
const SECRET_ACCESS_KEY =
    '0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8'

const s3Client = new AWS.S3({
    endpoint: `https://${ACCOUNT_ID}.r2.cloudflarestorage.com`,
    accessKeyId: ACCESS_KEY_ID,
    secretAccessKey: SECRET_ACCESS_KEY,
    signatureVersion: 'v4',
    region: 'auto',
})

interface CloudFlareFile extends BaseFile {
    title: string
}

interface CloudFlareImages {
    fieldName?: string
    id?: number
    imageType?: string
    logBookEntryID?: number
    logBookEntrySectionID?: number
    name?: string
    imageData?: string
}

export default function UploadCloudFlareCaptures({
    file = false,
    setFile,
    inputId,
    buttonType = 'icon',
    sectionData = { id: 0, sectionName: 'logBookEntryID' },
}: {
    file?: String[] | false
    setFile: any
    inputId?: string
    buttonType?: string
    sectionData?: { id: number; sectionName: string }
}) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const [openCameraDialog, setOpenCameraDialog] = useState(false)
    const [image, setImage] = useState<any>(false)
    const [displayImage, setDisplayImage] = useState(false)
    const [clientID, setClientID] = useState(0)
    const [devices, setDevices] = useState<MediaDeviceInfo[]>([])
    const [fileUpload, setFileUpload] = useState(false)
    const [uploadFileNames, setUploadFileNames] = useState<string[] | boolean>(
        false,
    )
    const [allImages, setAllImages] = useState<CloudFlareImages[]>([])
    const [allFiles, setAllFiles] = useState<CloudFlareFile[]>([])
    const bp = useBreakpoints()

    const getFile = (file: CloudFlareImages) => {
        if (!file || !file.name) {
            console.error('No file name provided')
            return
        }
        s3Client.getObject(
            {
                Bucket: 'captures',
                Key: file.name,
            },
            async (err, data) => {
                if (err) {
                    console.error(err)
                    if (file.id) {
                        deleteSectionMemberImage({
                            variables: {
                                ids: [+file.id],
                            },
                        })
                    }
                } else {
                    if (!file || !file.name) {
                        console.error('No file name provided')
                        return
                    }
                    const fileType = file.name.split('.').pop() || ''
                    const blob = new Blob([data?.Body as Uint8Array])
                    const url = URL.createObjectURL(blob)
                    if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {
                        setImage(url)
                        setDisplayImage(true)
                        const base64String = Buffer.from(
                            data?.Body as Uint8Array,
                        ).toString('base64')
                        const textContent = new TextDecoder().decode(
                            data?.Body as Uint8Array,
                        )
                        var base64Image = `data:image/${fileType};base64,${base64String}`
                        if (!textContent.startsWith('�PNG')) {
                            base64Image = textContent
                        }
                        setImage(base64Image)
                        if (
                            allImages.find((img) => img.name === file.name) ===
                            undefined
                        ) {
                            setAllImages(
                                (prev) =>
                                    [
                                        ...prev,
                                        {
                                            ...file,
                                            imageData: base64Image,
                                        },
                                    ] as CloudFlareImages[],
                            )
                        } else {
                            setAllImages((prev) =>
                                prev.map((img) =>
                                    img.name === file.name
                                        ? { ...img, imageData: base64Image }
                                        : img,
                                ),
                            )
                        }
                    } else {
                        const textContent = new TextDecoder().decode(
                            data?.Body as Uint8Array,
                        )
                        setImage(textContent)
                        if (
                            allImages.find((img) => img.name === file.name) ===
                            undefined
                        ) {
                            setAllImages(
                                (prev) =>
                                    [
                                        ...prev,
                                        {
                                            ...file,
                                            imageData: textContent,
                                        },
                                    ] as CloudFlareImages[],
                            )
                        } else {
                            setAllImages((prev) =>
                                prev.map((img) =>
                                    img.name === file.name
                                        ? { ...img, imageData: textContent }
                                        : img,
                                ),
                            )
                        }
                        setDisplayImage(true)
                    }
                }
            },
        )
    }

    // Handle opening the camera dialog
    const handleOpenCameraDialog = async (bypass = false) => {
        setOpenCameraDialog(true)
        setUploadFileNames(false)
        setFileUpload(false)
        setImage(null)
        setDisplayImage(false)
        if (file && file.length > 0 && !bypass) {
            file.forEach((f: any) => {
                getFile(f as CloudFlareImages)
            })
            return
        }
        const devices = await navigator.mediaDevices.enumerateDevices()
        const hasEnvironmentCamera = devices.some(
            (device) => device.kind === 'videoinput',
        )
        if (hasEnvironmentCamera) {
            setDevices(devices.filter((device) => device.kind === 'videoinput'))
        } else {
            toast({
                description: 'No camera found. Please connect a camera.',
                variant: 'destructive',
            })
            return
        }
        navigator.mediaDevices
            .getUserMedia({
                video: {
                    facingMode: 'environment',
                },
                audio: false,
            })
            .then((stream) => {
                const videoElement = document.getElementById(
                    'camera-video',
                ) as HTMLVideoElement
                videoElement.srcObject = stream
                videoElement.play()
            })
            .catch((error) => {
                console.error('Error accessing camera:', error)
            })
    }

    const captureImage = () => {
        const videoElement = document.getElementById(
            'camera-video',
        ) as HTMLVideoElement
        if (!videoElement) {
            console.error('Video element not found')
            return
        }
        const canvas = document.createElement('canvas')
        canvas.width = videoElement.videoWidth
        canvas.height = videoElement.videoHeight
        const context = canvas.getContext('2d')
        if (!context) {
            console.error('Failed to get canvas context')
            return
        }
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height)
        const imageData = canvas.toDataURL('image/png')

        // Stop the camera stream after capturing the image
        if (videoElement.srcObject) {
            const stream = videoElement.srcObject as MediaStream
            const tracks = stream.getTracks()
            tracks.forEach((track) => track.stop())
            videoElement.srcObject = null
        }
        if (imageData) {
            setImage(imageData)
            setDisplayImage(true)
            setAllImages((prev) => [
                ...prev,
                {
                    name: clientID + '-capture-' + Date.now(),
                    imageData: imageData,
                } as CloudFlareImages,
            ])
        }
    }

    const turnOffCamera = () => {
        const videoElement = document.getElementById(
            'camera-video',
        ) as HTMLVideoElement
        if (videoElement && videoElement.srcObject) {
            const stream = videoElement.srcObject as MediaStream
            const tracks = stream.getTracks()
            tracks.forEach((track) => track.stop())
            videoElement.srcObject = null
        }
    }

    useEffect(() => {
        setClientID(+(localStorage.getItem('clientId') ?? 0))
    }, [])

    useEffect(() => {
        if (openCameraDialog) return
        turnOffCamera()
    }, [openCameraDialog])

    useEffect(() => {
        if (allFiles.length > 0) {
            allFiles.forEach((file) => {
                if (file.title) {
                    getFile({
                        name: file.title,
                        fieldName: inputId,
                    } as CloudFlareImages)
                    setUploadFileNames((prev) => {
                        if (Array.isArray(prev)) {
                            return [...prev, file.title]
                        }
                        return [file.title]
                    })
                }
            })
        }
    }, [allFiles])

    const [createSectionMemberImage] = useMutation(
        CREATE_SECTION_MEMBER_IMAGE,
        {
            onCompleted: (response) => {
                const data = response.createSectionMemberImage
                setFile()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    async function uploadFile(file: CloudFlareImages) {
        // Upload file to Cloudflare
        var fileName
        if (file.imageData) {
            fileName = file.name || clientID + '-capture-' + Date.now()
        }
        if (!fileName) {
            fileName = clientID + '-capture-' + Date.now()
        }
        createSectionMemberImage({
            variables: {
                input: {
                    name: fileName,
                    fieldName: inputId,
                    imageType: 'FieldImage',
                    [sectionData.sectionName]:
                        sectionData.sectionName === 'logBookEntryID'
                            ? logentryID
                            : sectionData.id,
                },
            },
        })
        if (file.imageData) {
            s3Client.putObject(
                {
                    Bucket: 'captures',
                    Key: fileName,
                    Body: file.imageData,
                },
                (err, data) => {
                    if (err) {
                        console.error(err)
                    } else {
                        setFile()
                    }
                },
            )
        }
    }

    const switchCamera = () => {
        const videoElement = document.getElementById(
            'camera-video',
        ) as HTMLVideoElement
        if (!videoElement) {
            console.error('Video element not found')
            return
        }
        const currentDeviceId = videoElement.srcObject
            ? (videoElement.srcObject as MediaStream)
                  .getVideoTracks()[0]
                  .getSettings().deviceId
            : null

        const nextDevice = devices.find(
            (device) =>
                device.kind === 'videoinput' &&
                device.deviceId !== currentDeviceId,
        )

        if (nextDevice) {
            navigator.mediaDevices
                .getUserMedia({
                    video: { deviceId: nextDevice.deviceId },
                    audio: false,
                })
                .then((stream) => {
                    videoElement.srcObject = stream
                    videoElement.play()
                })
                .catch((error) => {
                    console.error('Error switching camera:', error)
                })
        } else {
            toast({
                description: 'No other camera found to switch.',
                variant: 'destructive',
            })
        }
    }

    const handleUploadFile = () => {
        if (allImages.length === 0) {
            toast({
                description: 'Please capture or upload an image first.',
                variant: 'destructive',
            })
            return
        }
        toast({
            description: 'Please capture or upload an image first.',
            variant: 'destructive',
        })
        allImages.forEach((img) => {
            uploadFile(img)
        })
        setAllImages([])
        setAllFiles([])
        setImage(null)
        setDisplayImage(false)
        setOpenCameraDialog(false)
        turnOffCamera()
        toast({
            description: 'Images uploaded successfully.',
        })
    }

    const [deleteSectionMemberImage] = useMutation(
        DELETE_SECTION_MEMBER_IMAGE,
        {
            onCompleted: (response) => {
                const data = response.deleteCaptureImage
                if (data) {
                    setFile()
                    toast({
                        description: 'Image deleted successfully.',
                    })
                }
            },
            onError: (error) => {
                console.error('Error deleting image', error)
                toast({
                    description: 'Failed to delete image.',
                    variant: 'destructive',
                })
            },
        },
    )

    const handleDeleteImage = (img: CloudFlareImages) => {
        setAllImages((prev) => prev.filter((image) => image.name !== img.name))
        if (img.imageData) {
            s3Client.deleteObject(
                {
                    Bucket: 'captures',
                    Key: img.name || '',
                },
                (err, data) => {
                    if (err) {
                        console.error('Error deleting image:', err)
                    } else {
                        toast({
                            description: 'Image deleted successfully.',
                        })
                    }
                },
            )
            if (!img.id) {
                return
            }
            deleteSectionMemberImage({
                variables: {
                    ids: [+img.id],
                },
            })
        }
    }

    return (
        <>
            <Button
                variant={buttonType === 'icon' ? 'ghost' : 'outline'}
                iconOnly={buttonType === 'icon'}
                size={buttonType === 'icon' ? 'icon' : 'default'}
                title="Add comment"
                className={buttonType === 'icon' ? 'group' : ''}
                iconLeft={
                    <Camera
                        className={
                            buttonType === 'icon'
                                ? cn(
                                      file && file.length > 0
                                          ? 'text-curious-blue-400 group-hover:text-curious-blue-400/50'
                                          : 'text-neutral-400 group-hover:text-neutral-400/50',
                                      'will-change-transform will-change-width will-change-padding transform-gpu',
                                      'group-hover:transition-colors group-hover:ease-out group-hover:duration-300',
                                  )
                                : ''
                        }
                        size={24}
                    />
                }
                onClick={() => handleOpenCameraDialog(false)}>
                {buttonType === 'button' && getResponsiveLabel(
                    bp.phablet,
                    'Capture / Upload',
                    'Capture / Upload Image',
                )}
            </Button>
            <AlertDialogNew
                openDialog={openCameraDialog}
                setOpenDialog={setOpenCameraDialog}
                title={fileUpload ? 'Files' : 'Camera'}
                handleCreate={() => {
                    if (image) {
                        handleUploadFile()
                        // setOpenCameraDialog(false)
                    } else {
                        toast({
                            description: 'Please capture an image first.',
                            variant: 'destructive',
                        })
                    }
                }}
                handleCancel={() => {
                    setOpenCameraDialog(false)
                    setImage(null)
                    setDisplayImage(false)
                    setAllImages([])
                    turnOffCamera()
                    setAllFiles([])
                    setFile()
                }}
                actionText="Save"
                cancelText="Close"
                loading={false}>
                <div className="flex flex-col items-center">
                    {allImages.length > 0 && (
                        <div className="flex flex-wrap mb-4">
                            {allImages.map((img, index) => (
                                <div
                                    className="w-1/4 p-1 rounded-md relative"
                                    key={index}>
                                    <img
                                        key={index}
                                        src={img.imageData}
                                        alt={`Captured ${index}`}
                                        className="object-cover"
                                        onClick={() => {
                                            setImage(img.imageData)
                                            setDisplayImage(true)
                                            turnOffCamera()
                                        }}
                                    />
                                    <Button
                                        variant="destructive"
                                        size="icon"
                                        className="absolute top-1 right-1 p-0 size-5"
                                        onClick={() => {
                                            handleDeleteImage(img)
                                        }}>
                                        &times;
                                    </Button>
                                </div>
                            ))}
                        </div>
                    )}
                    {fileUpload ? (
                        <UploadCloudFlare
                            files={allFiles}
                            setFiles={setAllFiles}
                            accept="image/*"
                            bucketName="captures"
                            multipleUpload={true}
                            prefix={logentryID + '-'}
                            displayFiles={false}
                        />
                    ) : (
                        <>
                            <video
                                id="camera-video"
                                style={{
                                    display: displayImage ? 'none' : 'block',
                                }}></video>
                            <img
                                src={image}
                                alt="Captured"
                                style={{
                                    display: displayImage ? 'block' : 'none',
                                }}
                            />
                        </>
                    )}
                </div>
                <div className="flex items-center mt-4 gap-2 justify-between">
                    {!displayImage && !fileUpload && (
                        <Button onClick={captureImage} className="mt-2">
                            Capture
                        </Button>
                    )}
                    {displayImage && !fileUpload && (
                        <>
                            <Button
                                onClick={() => {
                                    setImage(null)
                                    setDisplayImage(false)
                                    handleOpenCameraDialog(true)
                                }}
                                className="mt-2">
                                Recapture
                            </Button>
                            {/* <Button
                                onClick={() => {
                                    setAllImages((prev) => [...prev, image])
                                    setImage(null)
                                    setDisplayImage(false)
                                    handleOpenCameraDialog(true)
                                }}
                                className="mt-2">
                                Capture more
                            </Button> */}
                        </>
                    )}
                    {devices.length > 1 && (
                        <Button
                            onClick={() => {
                                switchCamera()
                            }}
                            className="mt-2">
                            Switch Camera
                        </Button>
                    )}
                    {fileUpload ? (
                        <Button
                            onClick={() => {
                                setFileUpload(false)
                                handleOpenCameraDialog()
                            }}
                            className="mt-2">
                            Capture Image
                        </Button>
                    ) : (
                        <Button
                            onClick={() => {
                                turnOffCamera()
                                setFileUpload(true)
                            }}
                            className="mt-2">
                            Upload Image
                        </Button>
                    )}
                </div>
            </AlertDialogNew>
        </>
    )
}

export const getCloudFlareImagesFile = (
    file: CloudFlareImages,
): Promise<string | undefined> => {
    return new Promise((resolve, reject) => {
        if (!file || !file.name) {
            console.error('No file name provided')
            reject('No file name provided')
            return
        }
        s3Client.getObject(
            {
                Bucket: 'captures',
                Key: file.name,
            },
            async (err, data) => {
                if (err) {
                    console.error(err)
                    reject(err)
                } else {
                    if (!file || !file.name) {
                        console.error('No file name provided')
                        reject('No file name provided')
                        return
                    }
                    const fileType = file.name.split('.').pop() || ''
                    const blob = new Blob([data?.Body as Uint8Array])
                    const url = URL.createObjectURL(blob)
                    if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {
                        const base64String = Buffer.from(
                            data?.Body as Uint8Array,
                        ).toString('base64')
                        const textContent = new TextDecoder().decode(
                            data?.Body as Uint8Array,
                        )
                        var base64Image = `data:image/${fileType};base64,${base64String}`
                        if (!textContent.startsWith('�PNG')) {
                            base64Image = textContent
                        }
                        resolve(base64Image)
                    } else {
                        const textContent = new TextDecoder().decode(
                            data?.Body as Uint8Array,
                        )
                        resolve(textContent)
                    }
                }
            },
        )
    })
}
