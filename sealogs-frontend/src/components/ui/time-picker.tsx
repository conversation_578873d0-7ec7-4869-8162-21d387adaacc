'use client'

import * as React from 'react'
import dayjs from 'dayjs'
import { Clock, ChevronUp, ChevronDown } from 'lucide-react'

import { Button, buttonVariants } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label, LabelPosition } from '@/components/ui/label'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { Switch } from './switch'
import { Separator } from './separator'
import { cn } from '@/app/lib/utils'

type TimePickerMode = 'single' | 'range'

interface TimePickerProps {
    value?: Date | dayjs.Dayjs
    onChange?: (d: Date) => void
    showSeconds?: boolean
    use24Hour?: boolean
    disabled?: boolean
    className?: string
    label?: string
    labelPosition?: LabelPosition
    mode?: TimePickerMode
    toValue?: Date | dayjs.Dayjs
    onToChange?: (d: Date) => void
    nowButton?: boolean
    nowButtonLabel?: string
}

interface TimeRangePickerProps {
    fromValue?: Date | dayjs.Dayjs
    toValue?: Date | dayjs.Dayjs
    onFromChange?: (d: Date) => void
    onToChange?: (d: Date) => void
    showSeconds?: boolean
    use24Hour?: boolean
    disabled?: boolean
    className?: string
    label?: string
    labelPosition?: LabelPosition
}

/* ---------- internal helpers ---------- */

const toDate = (v: Date | dayjs.Dayjs | undefined) => {
    if (!v) {
        return new Date()
    }
    return dayjs.isDayjs(v) ? v.toDate() : v
}

const isSameTime = (a: Date, b: Date) =>
    a.getHours() === b.getHours() &&
    a.getMinutes() === b.getMinutes() &&
    a.getSeconds() === b.getSeconds()

/* ---------- TimePicker ---------- */

export function TimePicker({
    value = new Date(),
    onChange,
    showSeconds = false,
    use24Hour = true,
    disabled = false,
    className,
    label,
    labelPosition = 'top',
    mode = 'single',
    toValue,
    onToChange,
    nowButton = false,
    nowButtonLabel = 'Set To Now',
}: TimePickerProps) {
    /* ---------------- state ---------------- */

    const [selectedTime, setSelectedTime] = React.useState<Date>(toDate(value))
    const [selectedToTime, setSelectedToTime] = React.useState<Date>(() => {
        if (mode !== 'range') return toDate(value)
        const base = toDate(toValue ?? value)
        return base
    })
    const [open, setOpen] = React.useState(false)
    const [is24Hour, setIs24Hour] = React.useState(use24Hour)

    /* --------- keep state in sync when parent changes --------- */

    React.useEffect(() => {
        const v = toDate(value)
        if (!isSameTime(v, selectedTime)) setSelectedTime(v)
    }, [value])

    React.useEffect(() => {
        if (!toValue) return
        const v = toDate(toValue)
        if (!isSameTime(v, selectedToTime)) setSelectedToTime(v)
    }, [toValue])

    /* ---------------- derived values ---------------- */

    const h = selectedTime.getHours()
    const m = selectedTime.getMinutes()
    const s = selectedTime.getSeconds()
    const isPM = h >= 12
    const dispH = is24Hour ? h : h % 12 || 12
    const pad = (n: number) => n.toString().padStart(2, '0')

    const baseTimeStr = `${pad(dispH)}:${pad(m)}${
        showSeconds ? `:${pad(s)}` : ''
    }${is24Hour ? '' : ` ${isPM ? 'PM' : 'AM'}`}`

    const rangeStr = (() => {
        if (mode !== 'range') return baseTimeStr
        const th = selectedToTime.getHours()
        const tm = selectedToTime.getMinutes()
        const ts = selectedToTime.getSeconds()
        const tIsPM = th >= 12
        const dispTH = is24Hour ? th : th % 12 || 12
        const toStr = `${pad(dispTH)}:${pad(tm)}${
            showSeconds ? `:${pad(ts)}` : ''
        }${is24Hour ? '' : ` ${tIsPM ? 'PM' : 'AM'}`}`
        return `${baseTimeStr} – ${toStr}`
    })()

    /* ---------------- pure setters ---------------- */

    const setTimeState = (updater: (d: Date) => void, to = false) => {
        const next = new Date(to ? selectedToTime : selectedTime)
        updater(next)
        to ? setSelectedToTime(next) : setSelectedTime(next)
    }

    /* ---------------- UI handlers ---------------- */

    const inc = (type: 'h' | 'm' | 's', step: 1 | -1, to = false) =>
        setTimeState((d) => {
            type === 'h'
                ? d.setHours(d.getHours() + step)
                : type === 'm'
                  ? d.setMinutes(d.getMinutes() + step)
                  : d.setSeconds(d.getSeconds() + step)
        }, to)

    const onInput = (
        e: React.ChangeEvent<HTMLInputElement>,
        type: 'h' | 'm' | 's',
    ) => {
        const val = Number(e.target.value)
        if (Number.isNaN(val)) return
        setTimeState((d) => {
            if (type === 'h') d.setHours(val)
            if (type === 'm') d.setMinutes(val)
            if (type === 's') d.setSeconds(val)
        })
    }

    const togglePeriod = () =>
        setTimeState((d) => d.setHours(d.getHours() + 12 * (isPM ? -1 : 1)))

    const handleSelect = () => {
        onChange?.(selectedTime)
        if (mode === 'range') onToChange?.(selectedToTime)
        setOpen(false)
    }

    const handleSetToNow = (e: React.MouseEvent) => {
        e.stopPropagation()
        const now = dayjs().toDate()
        setSelectedTime(now)
        onChange?.(now)
    }

    /* ------------------- UI ------------------- */

    const picker = (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger className="group" asChild>
                <div
                    className={cn(
                        buttonVariants({ variant: 'outline' }),
                        'group-hover:bg-muted justify-between',
                        nowButton ? 'flex-none !pr-1 py-1' : 'w-full py-0',
                        className,
                    )}>
                    <Button
                        variant="text"
                        iconLeft={Clock}
                        className={cn('justify-start !px-0 w-full')}
                        disabled={disabled}>
                        {rangeStr}
                    </Button>
                    {nowButton && (
                        <Button
                            className="h-full rounded py-0"
                            onClick={handleSetToNow}
                            disabled={disabled}>
                            {nowButtonLabel}
                        </Button>
                    )}
                </div>
            </PopoverTrigger>

            <PopoverContent className="w-auto p-4" align="start">
                {/* 24-hour toggle */}
                <Label
                    htmlFor="tfmt"
                    label="24-hour format"
                    rightContent={
                        <Switch
                            id="tfmt"
                            checked={is24Hour}
                            onCheckedChange={setIs24Hour}
                        />
                    }
                />

                {/* main time controls */}
                <div className="flex items-center space-x-2">
                    {/* hours */}
                    <div className="flex flex-col items-center">
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => inc('h', 1)}>
                            <ChevronUp className="h-4 w-4" />
                        </Button>
                        <Input
                            className="h-9 w-[4rem] text-center"
                            value={pad(dispH)}
                            onChange={(e) => onInput(e, 'h')}
                            inputMode="numeric"
                            pattern="[0-9]*"
                        />
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => inc('h', -1)}>
                            <ChevronDown className="h-4 w-4" />
                        </Button>
                    </div>

                    <span className="text-xl text-background0">:</span>

                    {/* minutes */}
                    <div className="flex flex-col items-center">
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => inc('m', 1)}>
                            <ChevronUp className="h-4 w-4" />
                        </Button>
                        <Input
                            className="h-9 w-[4rem] text-center"
                            value={pad(m)}
                            onChange={(e) => onInput(e, 'm')}
                            inputMode="numeric"
                            pattern="[0-9]*"
                        />
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => inc('m', -1)}>
                            <ChevronDown className="h-4 w-4" />
                        </Button>
                    </div>

                    {/* seconds (optional) */}
                    {showSeconds && (
                        <>
                            <span className="text-xl text-background0">:</span>
                            <div className="flex flex-col items-center">
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8"
                                    onClick={() => inc('s', 1)}>
                                    <ChevronUp className="h-4 w-4" />
                                </Button>
                                <Input
                                    className="h-9 w-[4rem] text-center"
                                    value={pad(s)}
                                    onChange={(e) => onInput(e, 's')}
                                    inputMode="numeric"
                                    pattern="[0-9]*"
                                />
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8"
                                    onClick={() => inc('s', -1)}>
                                    <ChevronDown className="h-4 w-4" />
                                </Button>
                            </div>
                        </>
                    )}

                    {/* AM/PM toggle */}
                    {!is24Hour && (
                        <Button
                            variant="outline"
                            className="ml-2 h-9 px-3"
                            onClick={togglePeriod}>
                            {isPM ? 'PM' : 'AM'}
                        </Button>
                    )}
                </div>

                {/* range extra controls */}
                {mode === 'range' && (
                    <>
                        <Separator className="my-3" />
                        {/* very similar controls for selectedToTime … omitted for brevity.
                replicate above block, passing `true` flag to setters */}
                        {/* ... */}
                    </>
                )}

                <Button className="w-full mt-4" onClick={handleSelect}>
                    Select
                </Button>
            </PopoverContent>
        </Popover>
    )

    return label ? (
        <Label label={label} position={labelPosition} disabled={disabled}>
            {picker}
        </Label>
    ) : (
        picker
    )
}

/* ---------- TimeRangePicker ---------- */

export function TimeRangePicker({
    fromValue = new Date(),
    toValue = new Date(),
    onFromChange,
    onToChange,
    showSeconds,
    use24Hour,
    disabled,
    className,
    label,
    labelPosition = 'top',
}: TimeRangePickerProps) {
    return (
        <Label
            label={label}
            position={labelPosition}
            disabled={disabled}
            className={className}>
            <TimePicker
                value={fromValue}
                toValue={toValue}
                onChange={onFromChange}
                onToChange={onToChange}
                showSeconds={showSeconds}
                use24Hour={use24Hour}
                disabled={disabled}
                mode="range"
                className="w-full"
            />
        </Label>
    )
}
