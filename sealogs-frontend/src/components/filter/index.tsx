'use client'

import { usePathname } from 'next/navigation'
import VesselDropdown from './components/vessel-dropdown'
import TrainingTypeDropdown from './components/training-type-dropdown'
import CrewDropdown from './components/crew-dropdown/crew-dropdown'
import DateRange from '../DateRange'
import CrewDutyDropdown from './components/crew-duty-dropdown'
import TrainingStatusDropdown from './components/training-status-dropdown'
import { debounce } from 'lodash'
import SupplierDropdown from './components/supplier-dropdown'
import CategoryDropdown from './components/category-dropdown'
import { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Combobox } from '@/components/ui/comboBox'
import MaintenanceCategoryDropdown from './components/maintenance-category-dropdown'

import { CrewTrainingFilterActions } from './components/training-actions'
import { Label } from '@/components/ui/label'
import TimeField from '@/app/ui/logbook/components/time'
import dayjs from 'dayjs'
import LocationField from '@/app/ui/logbook/components/location'
import type { DateR<PERSON><PERSON> as TDateRange } from 'react-day-picker'
import { Button } from '@/components/ui/button'
import { CheckIcon } from 'lucide-react'
import SeaLogsButton from '../ui/sea-logs-button'
import { Card, CardContent, Tabs, TabsList, TabsTrigger } from '../ui'
import { cn } from '@/app/lib/utils'
import { useMediaQuery } from '@reactuses/core'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion'
import { useBreakpoints } from '../hooks/useBreakpoints'

const Filter = ({
    onChange,
    vesselIdOptions = [],
    trainingTypeIdOptions = [],
    memberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
    supplierIdOptions = [],
    categoryIdOptions = [],
    onClick,
    crewData,
    vesselData,
    tripReportFilterData = {},
    table,
}: any) => {
    const pathname = usePathname()
    const [selectedOptions, setSelectedOptions] = useState({
        vessel: null,
        supplier: null,
        category: null,
    })
    const [filteredOptions, setFilteredOptions] = useState({
        vesselIdOptions,
        supplierIdOptions,
        categoryIdOptions,
    })

    const handleOnChange = ({ type, data }: any) => {
        const newSelectedOptions = { ...selectedOptions, [type]: data }
        setSelectedOptions(newSelectedOptions)

        filterOptions(newSelectedOptions)

        onChange({ type, data })
    }

    const filterOptions = (selectedOptions: any) => {
        let newSupplierIdOptions = supplierIdOptions
        let newCategoryIdOptions = categoryIdOptions

        if (selectedOptions.vessel) {
            newSupplierIdOptions = supplierIdOptions.filter((supplier: any) => {
                return supplier.vesselId === selectedOptions.vessel.id
            })
        }

        if (selectedOptions.supplier) {
            newCategoryIdOptions = categoryIdOptions.filter((category: any) => {
                return category.supplierId === selectedOptions.supplier.id
            })
        }

        setFilteredOptions({
            vesselIdOptions: vesselIdOptions,
            supplierIdOptions: newSupplierIdOptions,
            categoryIdOptions: newCategoryIdOptions,
        })
    }

    const handleOnClick = () => {
        onClick()
    }
    return (
        <div>
            {/* <Label>Filter</Label> */}
            <div>
                {pathname === '/vessel' && (
                    <VesselListFilter table={table} onChange={handleOnChange} />
                )}
                {pathname === '/crew-training' && (
                    <TrainingListFilter
                        onChange={handleOnChange}
                        vesselIdOptions={vesselIdOptions}
                        trainingTypeIdOptions={trainingTypeIdOptions}
                        memberId={memberId}
                        trainerIdOptions={trainerIdOptions}
                        memberIdOptions={memberIdOptions}
                    />
                )}
                {pathname === '/crew/info' && (
                    <AllocatedTasksFilter onChange={handleOnChange} />
                )}
                {pathname === '/crew' && (
                    <CrewListFilter onChange={handleOnChange} />
                )}
                {pathname === '/inventory' && (
                    <InventoryListFilter
                        onChange={handleOnChange}
                        vesselIdOptions={filteredOptions.vesselIdOptions}
                        supplierIdOptions={filteredOptions.supplierIdOptions}
                        categoryIdOptions={filteredOptions.categoryIdOptions}
                    />
                )}
                {pathname === '/inventory/suppliers' && (
                    <SupplierListFilter onChange={handleOnChange} />
                )}
                {pathname === '/key-contacts' && (
                    <SearchInputOnlyFilter onChange={handleOnChange} />
                )}
                {pathname === '/maintenance' && (
                    <MaintenanceListFilter onChange={handleOnChange} />
                )}
                {pathname === '/training-type' && (
                    <TrainingTypeListFilter onChange={handleOnChange} />
                )}
                {pathname === '/reporting' && (
                    <ReporingFilters
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                        crewData={crewData}
                        vesselData={vesselData}
                    />
                )}
                {pathname === '/reporting/crew-seatime-report' && (
                    <CrewSeatimeReportFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {pathname === '/reporting/crew-training-completed-report' && (
                    <TrainingCompletedReportFilter
                        onChange={handleOnChange}
                        vesselIdOptions={vesselIdOptions}
                        trainingTypeIdOptions={trainingTypeIdOptions}
                        memberId={memberId}
                        trainerIdOptions={trainerIdOptions}
                        memberIdOptions={memberIdOptions}
                    />
                )}
                {pathname === '/reporting/simple-fuel-report' && (
                    <MultiVesselsDateRangeFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {pathname === '/reporting/engine-hours-report' && (
                    <MultiVesselsDateRangeFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {pathname === '/reporting/service-report' && (
                    <MultiVesselsDateRangeFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {pathname === '/reporting/activity-reports' && (
                    <ActivityReportFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {(pathname === '/reporting/maintenance-status-activity' ||
                    pathname === '/reporting/maintenance-cost-track') && (
                    <MaintenanceReportFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {(pathname === '/reporting/fuel-analysis' ||
                    pathname === '/reporting/fuel-tasking-analysis' ||
                    pathname === '/reporting/detailed-fuel-report' ||
                    pathname === '/reporting/fuel-summary-report') && (
                    <FuelReporingFilters onChange={handleOnChange} />
                )}
                {pathname === '/document-locker' && (
                    <DocumentLockerFilter onChange={handleOnChange} />
                )}
                {pathname === '/calendar' && (
                    <CalendarFilter onChange={handleOnChange} />
                )}
                {pathname === '/reporting/trip-report' && (
                    <TripReportFilters
                        tripReportFilterData={tripReportFilterData}
                        onChange={handleOnChange}
                    />
                )}
            </div>
        </div>
    )
}

export default Filter

const VesselListFilter = ({ onChange, table }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    return (
        <div className="flex flex-1 items-center justify-between">
            <Input
                type="search"
                placeholder="Search"
                value={
                    (table.getAllColumns()?.[0]?.getFilterValue() as string) ??
                    ''
                }
                onChange={(event: any) =>
                    table
                        .getAllColumns()?.[0]
                        ?.setFilterValue(event.target.value)
                }
                className="h-11 w-[150px] lg:w-[250px]"
            />
        </div>
    )
}
//
export const TrainingListFilter = ({
    onChange,
    vesselIdOptions = [],
    trainingTypeIdOptions = [],
    memberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
    overdueSwitcher = false,
    excludeFilters = [],
    // setOverdueSwitcher,
}: any) => {
    const [overdueList, setOverdueList] = useState(overdueSwitcher)

    useEffect(() => {
        setOverdueList(overdueSwitcher)
    }, [overdueSwitcher])

    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    const bp = useBreakpoints()

    const filterContent = (
        <div className="grid xs:grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2.5">
            {!overdueList !== true && !excludeFilters.includes('dateRange') && (
                <DateRange
                    onChange={(data: any) =>
                        handleDropdownChange('dateRange', data)
                    }
                    clearable
                />
            )}

            {!excludeFilters.includes('trainingType') && (
                <TrainingTypeDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('trainingType', data)
                    }
                    trainingTypeIdOptions={trainingTypeIdOptions}
                />
            )}

            {!excludeFilters.includes('vessel') && (
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    vesselIdOptions={vesselIdOptions}
                />
            )}

            {!overdueList !== true && !excludeFilters.includes('trainer') && (
                <CrewDropdown
                    label=""
                    placeholder="Trainer"
                    isClearable={true}
                    multi
                    controlClasses="filter"
                    onChange={(data: any) =>
                        handleDropdownChange('trainer', data)
                    }
                    filterByTrainingSessionMemberId={memberId}
                    trainerIdOptions={trainerIdOptions}
                />
            )}

            {!excludeFilters.includes('crew') &&
                !excludeFilters.includes('member') && (
                    <CrewDropdown
                        isClearable={true}
                        label=""
                        multi
                        controlClasses="filter"
                        placeholder="Crew"
                        onChange={(data: any) =>
                            handleDropdownChange('member', data)
                        }
                        filterByTrainingSessionMemberId={memberId}
                        memberIdOptions={memberIdOptions}
                    />
                )}
        </div>
    )

    return (
        <>
            {bp.phablet ? (
                filterContent
            ) : (
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="maintenance-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            )}
        </>
    )
}
const TrainingCompletedReportFilter = ({
    onChange,
    vesselIdOptions = [],
    trainingTypeIdOptions = [],
    memberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
}: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    const [overdueList, setOverdueList] = useState(true)

    return (
        <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    vesselIdOptions={vesselIdOptions}
                />
                <TrainingTypeDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('trainingType', data)
                    }
                    trainingTypeIdOptions={trainingTypeIdOptions}
                />
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    onChange={(data: any) =>
                        handleDropdownChange('trainer', data)
                    }
                    filterByTrainingSessionMemberId={memberId}
                    trainerIdOptions={trainerIdOptions}
                />
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) =>
                        handleDropdownChange('member', data)
                    }
                    filterByTrainingSessionMemberId={memberId}
                    memberIdOptions={memberIdOptions}
                />
            </div>
            <div className="flex">
                <CrewTrainingFilterActions
                    onChange={(data: any) => {
                        handleDropdownChange('overdue', data)
                    }}
                    overdueList={overdueList}
                />
            </div>
        </div>
    )
}
const CrewListFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    return (
        <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />
                <CrewDutyDropdown
                    crewDutyID={0}
                    hideCreateOption
                    onChange={(data: any) => {
                        handleDropdownChange('crewDuty', data)
                    }}
                />
                <TrainingStatusDropdown
                    isClearable={true}
                    onChange={(data: any) => {
                        handleDropdownChange('trainingStatus', data)
                    }}
                />
                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
            </div>
            {/* <div className="flex">
                <CrewFilterActions
                    onChange={(data: any) => {
                        handleDropdownChange('isArchived', data)
                    }}
                />
            </div> */}
        </div>
    )
}

const SearchInput = ({ onChange, className }: any) => {
    const debouncedOnChange = debounce(onChange, 600)

    const handleChange = (e: any) => {
        debouncedOnChange({ value: e.target.value })
    }
    return (
        <Input
            type="search"
            className={cn('h-11 w-full lg:w-[250px]', className)}
            placeholder="Search..."
            onChange={handleChange}
        />
    )
}

const InventoryListFilter = ({
    onChange,
    vesselIdOptions,
    supplierIdOptions,
    categoryIdOptions,
}: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    return (
        <div className="flex flex-1 items-center justify-between gap-2.5">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <VesselDropdown
                    isClearable={true}
                    vesselIdOptions={vesselIdOptions}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    //className="min-w-52"
                />
                <SupplierDropdown
                    isClearable={true}
                    supplierIdOptions={supplierIdOptions}
                    onChange={(data: any) =>
                        handleDropdownChange('supplier', data)
                    }
                    //className="min-w-52"
                />
                <CategoryDropdown
                    isClearable={true}
                    categoryIdOptions={categoryIdOptions}
                    onChange={(data: any) =>
                        handleDropdownChange('category', data)
                    }
                    //className="min-w-52"
                />
                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
            </div>
        </div>
    )
}
const SupplierListFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    return (
        <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
            </div>
        </div>
    )
}

const SearchInputOnlyFilter = ({ onChange }: any) => {
    return (
        <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <SearchInput
                    onChange={(data: any) => {
                        onChange({ type: 'keyword', data })
                    }}
                />
            </div>
        </div>
    )
}

const MaintenanceListFilter = ({ onChange }: any) => {
    const isSmallScreen = useMediaQuery('(max-width: 479px)') // Below xs breakpoint (480px)
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const filterContent = (
        <div className="flex flex-1 flex-wrap items-start justify-between gap-2.5">
            <div className="w-full lg:w-auto grid small:grid-cols-2 tablet-sm:grid-cols-3 sm:grid-cols-4 lg:grid-cols-4 gap-2.5">
                <div className="col-auto small:col-span-2 tablet-sm:col-auto">
                    <DateRange
                        className="border"
                        clearable
                        placeholder="Due Date Range"
                        onChange={(data: any) =>
                            handleDropdownChange('dateRange', data)
                        }
                    />
                </div>
                <MaintenanceStatusDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('status', data)
                    }
                />

                <MaintenanceCategoryDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('category', data)
                    }
                />

                <MaintenanceRecurringDropdown
                    onChange={(data: any) =>
                        handleDropdownChange('recurring', data)
                    }
                />

                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) =>
                        handleDropdownChange('member', data)
                    }
                />

                <VesselDropdown
                    className="small:col-span-2 tablet-sm:col-span-3"
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />
            </div>
            {!isSmallScreen && (
                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
            )}
        </div>
    )

    if (isSmallScreen) {
        return (
            <>
                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="maintenance-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            </>
        )
    }

    return filterContent
}
const MaintenanceStatusDropdown = ({ onChange }: any) => {
    const [isLoading, setIsLoading] = useState(true)
    const [selectedValue, setSelectedValue] = useState()
    const statusOptions = [
        { value: 'Open', label: 'Open' },
        { value: 'Save_As_Draft', label: 'Save as Draft' },
        { value: 'In_Progress', label: 'In Progress' },
        { value: 'On_Hold', label: 'On Hold' },
        { value: 'Overdue', label: 'Overdue' },
        { value: 'Completed', label: 'Completed' },
    ]

    useEffect(() => {
        setIsLoading(false)
    }, [])

    return (
        <>
            {statusOptions && !isLoading && (
                // <SLSelect
                //     id="supplier-dropdown"
                //     closeMenuOnSelect={true}
                //     options={statusOptions}
                //     menuPlacement="top"
                //     // defaultValue={selectedSupplier}
                //     // value={selectedSupplier}
                //     onChange={onChange}
                //     isClearable={true}
                //     placeholder="Status"
                // />

                <Combobox
                    options={statusOptions}
                    value={selectedValue}
                    onChange={(selectedOption: any) => {
                        setSelectedValue(selectedOption)
                        onChange(selectedOption)
                    }}
                    title="Status"
                    placeholder="Status"
                    /*''
                    classNames={{
                        control: () => classes.selectControl + ' !min-w-48',
                        singleValue: () => classes.selectSingleValue,
                        menu: () => classes.selectMenu,
                        option: () => '',
                    }}*/
                />
            )}
        </>
    )
}

const MaintenanceRecurringDropdown = ({ onChange }: any) => {
    const [selectedValue, setSelectedValue] = useState()

    const recurringOptions = [
        { value: 'recurring', label: 'Recurring' },
        { value: 'one-off', label: 'One-off' },
    ]

    const handleOnChange = (value: any) => {
        setSelectedValue(value)
        onChange(value)
    }

    return (
        <Combobox
            options={recurringOptions}
            value={selectedValue}
            onChange={handleOnChange}
            placeholder="Task Type"
        />
    )
}

const TrainingTypeListFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    return (
        <div className="grid grid-cols-5 gap-2.5">
            <VesselDropdown
                isClearable={true}
                className="col-span-3 sm:col-span-2"
                onChange={(data: any) => handleDropdownChange('vessel', data)}
            />
            <div className="col-span-2 sm:col-span-1 col-end-6 sm:col-end-6">
                <SearchInput
                    className="!w-full"
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
            </div>
        </div>
    )
}
const ReporingFilters = ({
    onChange,
    onClickButton,
    crewData,
    vesselData,
}: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const [crewIsMulti, setCrewIsMulti] = useState(true)
    const [vesselIsMulti, setVesselIsMulti] = useState(true)

    const getReport = () => {
        onClickButton()
    }

    useEffect(() => {
        if (crewData.length > 1) {
            setVesselIsMulti(false)
        } else {
            setVesselIsMulti(true)
        }

        if (vesselData.length > 1) {
            setCrewIsMulti(false)
        } else {
            setCrewIsMulti(true)
        }
    }, [crewData, vesselData])
    return (
        <div className="flex flex-col md:flex-row gap-2.5 w-full">
            <div className="mr-2">
                <DateRange
                    className="border "
                    onChange={(data: any) =>
                        handleDropdownChange('dateRange', data)
                    }
                />
            </div>
            <div className="mr-2">
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) =>
                        handleDropdownChange('member', data)
                    }
                    isMulti={crewIsMulti}
                />
            </div>
            <div className="mr-2">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    isMulti={vesselIsMulti}
                />
            </div>
            <div className="mr-2">
                <SeaLogsButton
                    text={'Report'}
                    type="primary"
                    color="sky"
                    action={getReport}
                />
            </div>
        </div>
    )
}
const FuelReporingFilters = ({ onChange }: any) => {
    const [dateRange, setDaterange] = useState<TDateRange>({
        from: new Date(),
        to: new Date(),
    })

    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    return (
        <div className="flex flex-col md:flex-row gap-2.5 w-full">
            <div className="mr-2">
                <DateRange
                    type="date"
                    mode="range"
                    value={dateRange}
                    dateFormat="MMM do, yyyy"
                    onChange={(data: any) => {
                        setDaterange({
                            from: data?.startDate,
                            to: data?.endDate,
                        })
                        handleDropdownChange('dateRange', data)
                    }}
                />
            </div>
            <div className="mr-2">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />
            </div>
        </div>
    )
}
const DocumentLockerFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    return (
        <div className="flex flex-col justify-between md:flex-row gap-2.5 flex-1">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    classesName="min-w-52"
                />

                <DocumentModuleDropdown
                    onChange={(data: any) => {
                        handleDropdownChange('Module', data)
                    }}
                    classesName="min-w-52"
                />
            </div>

            <SearchInput
                onChange={(data: any) => {
                    handleDropdownChange('keyword', data)
                }}
            />
        </div>
    )
}
const DocumentModuleDropdown = ({ onChange, multi = true, className }: any) => {
    const [isLoading, setIsLoading] = useState(true)
    const [selectedDocumentModule, setSelectedDocumentModule] = useState(
        [] as any,
    )

    const statusOptions = [
        { value: 'Vessel', label: 'Vessel' },
        { value: 'Maintenance', label: 'Maintenance' },
        { value: 'Inventory', label: 'Inventory' },
        { value: 'Company', label: 'Company' },
    ]

    useEffect(() => {
        setIsLoading(false)
    }, [])

    const handleOnChange = (selectedOption: any) => {
        setSelectedDocumentModule(selectedOption)
        onChange(selectedOption)
    }

    return (
        <>
            {statusOptions && !isLoading && (
                <Combobox
                    options={statusOptions}
                    value={selectedDocumentModule}
                    onChange={handleOnChange}
                    title="Module"
                    placeholder="Module"
                    className={className}
                    multi={multi} // Enables multi-select when needed
                />
            )}
        </>
    )
}
const CalendarModuleDropdpown = ({ onChange }: any) => {
    const [isLoading, setIsLoading] = useState(true)

    const statusOptions = [
        { value: 'Task', label: 'Maintenance' },
        { value: 'Completed Training', label: 'Completed Training' },
        { value: 'Training Due', label: 'Training Due' },
        { value: 'Log Book Entry', label: 'Log Book Entry' },
    ]

    useEffect(() => {
        setIsLoading(false)
    }, [])

    return (
        <>
            {statusOptions && !isLoading && (
                <Combobox
                    id="document-module-dropdown"
                    options={statusOptions}
                    onChange={(element) => {
                        onChange('Module', element)
                    }}
                    className="max-w-[200px]"
                    placeholder="Module"
                />
            )}
        </>
    )
}

const CalendarFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    return (
        <Card>
            <CardContent className="flex gap-2.5">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) =>
                        handleDropdownChange('member', data)
                    }
                />

                <CalendarModuleDropdpown
                    onChange={(module: any, data: any) => {
                        handleDropdownChange('Module', data)
                    }}
                />
            </CardContent>
        </Card>
    )
}

const CrewSeatimeReportFilter = ({ onChange, onClickButton }: any) => {
    const [tab, setTab] = useState('detailed')
    const [dateRange, setDaterange] = useState<TDateRange>({
        from: new Date(),
        to: new Date(),
    })

    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const getReport = () => {
        onClickButton()
    }

    return (
        <div className="flex flex-col gap-2.5">
            <div className="flex justify-start">
                <Tabs
                    value={tab}
                    onValueChange={(value) => {
                        setTab(value)
                        handleDropdownChange('reportMode', value)
                    }}>
                    <TabsList>
                        <TabsTrigger value="detailed">
                            Detailed View
                        </TabsTrigger>
                        <TabsTrigger value="summary">
                            Summarized View
                        </TabsTrigger>
                    </TabsList>
                </Tabs>
            </div>
            <div className="flex flex-col md:flex-row gap-2.5 w-full">
                <div>
                    <DateRange
                        type="date"
                        mode="range"
                        value={dateRange}
                        dateFormat="MMM do, yyyy"
                        onChange={(data: any) => {
                            setDaterange({
                                from: data?.startDate,
                                to: data?.endDate,
                            })
                            handleDropdownChange('dateRange', data)
                        }}
                    />
                </div>
                <div>
                    <CrewDropdown
                        isClearable={true}
                        controlClasses="filter"
                        placeholder="Crew"
                        onChange={(data: any) => {
                            handleDropdownChange('members', data)
                        }}
                        isMulti={true}
                    />
                </div>
                <div>
                    <VesselDropdown
                        isClearable={true}
                        onChange={(data: any) =>
                            handleDropdownChange('vessels', data)
                        }
                        isMulti={true}
                    />
                </div>
                <div>
                    <CrewDutyDropdown
                        crewDutyID={0}
                        onChange={(data: any) => {
                            handleDropdownChange('crewDuty', data)
                        }}
                    />
                </div>
            </div>
            <div>
                <Button type="button" iconLeft={CheckIcon} onClick={getReport}>
                    Apply Filter
                </Button>
            </div>
        </div>
    )
}

const MultiVesselsDateRangeFilter = ({ onChange, onClickButton }: any) => {
    const [dateRange, setDaterange] = useState<TDateRange>({
        from: new Date(),
        to: new Date(),
    })

    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const getReport = () => {
        onClickButton()
    }

    return (
        <div className="flex flex-col md:flex-row md:items-center gap-2.5 mt-2">
            <div>
                <DateRange
                    type="date"
                    mode="range"
                    value={dateRange}
                    dateFormat="MMM do, yyyy"
                    onChange={(data: any) => {
                        setDaterange({
                            from: data?.startDate,
                            to: data?.endDate,
                        })
                        handleDropdownChange('dateRange', data)
                    }}
                />
            </div>
            <div>
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessels', data)
                    }
                    isMulti={true}
                />
            </div>

            <div>
                <Button type="button" iconLeft={CheckIcon} onClick={getReport}>
                    Apply Filter
                </Button>
            </div>
        </div>
    )
}

const ActivityReportFilter = ({ onChange, onClickButton }: any) => {
    const [dateRange, setDaterange] = useState<TDateRange>({
        from: new Date(),
        to: new Date(),
    })
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    const [selectedValue, setSelectedValue] = useState()

    const getReport = () => {
        onClickButton()
    }

    return (
        <div className="flex flex-col md:flex-row gap-2.5 mt-2 w-full">
            <div>
                <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                    <div className="flex items-center">
                        {/*<Select
                            id="activity-report-type-dropdown"
                            closeMenuOnSelect={true}
                            options={activityReportTypes}
                            menuPlacement="top"
                            onChange={(data: any) =>
                                handleDropdownChange(
                                    'report_type',
                                    data?.value ?? '',
                                )
                            }
                            className={}
                            placeholder="Activity Report Type"
                            classNames={{
                                control: () =>
                                    classes.selectControl + ' w-full',
                            }}
                            styles={{
                                container: (provided) => ({
                                    ...provided,
                                    width: '100%',
                                }),
                            }}
                        />*/}
                    </div>
                </div>
            </div>
            <div className="flex flex-col md:flex-row gap-2.5 w-full">
                <DateRange
                    type="date"
                    mode="range"
                    value={dateRange}
                    dateFormat="MMM do, yyyy"
                    onChange={(data: any) => {
                        setDaterange({
                            from: data?.startDate,
                            to: data?.endDate,
                        })
                        handleDropdownChange('dateRange', data)
                    }}
                />
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessels', data)
                    }
                    isMulti={true}
                />
                <Button type="button" iconLeft={CheckIcon} onClick={getReport}>
                    Apply Filter
                </Button>
            </div>
        </div>
    )
}

const MaintenanceReportFilter = ({ onChange, onClickButton }: any) => {
    const [dateRange, setDaterange] = useState<TDateRange>({
        from: new Date(),
        to: new Date(),
    })
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const getReport = () => {
        onClickButton()
    }

    return (
        <div className="flex flex-col">
            <div className="flex flex-col md:flex-row gap-2.5 mt-2 w-full">
                <div>
                    <DateRange
                        type="date"
                        mode="range"
                        value={dateRange}
                        dateFormat="MMM do, yyyy"
                        onChange={(data: any) => {
                            setDaterange({
                                from: data?.startDate,
                                to: data?.endDate,
                            })
                            handleDropdownChange('dateRange', data)
                        }}
                    />
                </div>
                <div>
                    <VesselDropdown
                        isClearable={true}
                        onChange={(data: any) =>
                            handleDropdownChange('vessels', data)
                        }
                        isMulti={true}
                    />
                </div>

                <div>
                    <MaintenanceCategoryDropdown
                        isClearable={true}
                        onChange={(data: any) =>
                            handleDropdownChange('category', data)
                        }
                    />
                </div>
            </div>

            <div className="flex flex-col md:flex-row gap-2.5 mt-2 w-full">
                <div>
                    <MaintenanceStatusDropdown
                        onChange={(data: any) =>
                            handleDropdownChange('status', data)
                        }
                    />
                </div>

                <div>
                    <CrewDropdown
                        isClearable={true}
                        controlClasses="filter"
                        placeholder="Allocated Crew"
                        onChange={(data: any) =>
                            handleDropdownChange('member', data)
                        }
                    />
                </div>

                <div>
                    <Button
                        type="button"
                        iconLeft={CheckIcon}
                        onClick={getReport}>
                        Apply Filter
                    </Button>
                </div>
            </div>
        </div>
    )
}

const TripReportFilters = ({ tripReportFilterData, onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    return (
        <div className="flex flex-col md:flex-row gap-2.5 w-full">
            <div className="mr-2">
                <DateRange
                    className="border border-slblue-200"
                    onChange={(data: any) =>
                        handleDropdownChange('dateRange', data)
                    }
                />
            </div>
            <div className="mr-2">
                <LocationField
                    handleLocationChange={(value: any) => {
                        // If value is null or undefined, return early
                        if (!value) {
                            handleDropdownChange('fromLocation', null)
                            return
                        }

                        // Pass the value directly to handleDropdownChange
                        handleDropdownChange('fromLocation', value)
                    }}
                    setCurrentLocation={() => {}}
                    currentEvent={{}}
                    showAddNewLocation={false}
                    showUseCoordinates={false}
                    showCurrentLocation={false}
                />
            </div>
            <div className="mr-2">
                <LocationField
                    handleLocationChange={(value: any) => {
                        // If value is null or undefined, return early
                        if (!value) {
                            handleDropdownChange('toLocation', null)
                            return
                        }

                        // Pass the value directly to handleDropdownChange
                        handleDropdownChange('toLocation', value)
                    }}
                    setCurrentLocation={() => {}}
                    currentEvent={{}}
                    showAddNewLocation={false}
                    showUseCoordinates={false}
                    showCurrentLocation={false}
                />
            </div>
            <div className="mr-2">
                <TimeField
                    time={tripReportFilterData.fromTime ?? ''}
                    timeID="from-time"
                    fieldName="From"
                    buttonLabel="Set To Now"
                    hideButton={true}
                    handleTimeChange={(data: any) =>
                        handleDropdownChange(
                            'fromTime',
                            dayjs(data).format('HH:mm'),
                        )
                    }
                />
            </div>
            <div className="mr-2">
                <TimeField
                    time={tripReportFilterData.toTime ?? ''}
                    timeID="to-time"
                    fieldName="To"
                    buttonLabel="Set To Now"
                    hideButton={true}
                    handleTimeChange={(data: any) =>
                        handleDropdownChange(
                            'toTime',
                            dayjs(data).format('HH:mm'),
                        )
                    }
                />
            </div>
            <div className="mr-2">
                <div className={`flex items-center my-4 w-full`}>
                    <Label
                        className={`relative flex items-center pr-3 rounded-full cursor-pointer`}
                        htmlFor="client-use-department"
                        data-ripple="true"
                        data-ripple-color="dark"
                        data-ripple-dark="true">
                        <Input
                            type="checkbox"
                            id="client-use-department"
                            className="before:content[''] peer relative h-5 w-5 cursor-pointer p-3 appearance-none rounded-full border border-sky-400 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-sky-500 before:opacity-0 before:transition-opacity checked:border-sky-700 checked:bg-sky-700 before:bg-sky-700 hover:before:opacity-10"
                            defaultChecked={tripReportFilterData.noPax}
                            onChange={(e: any) => {
                                handleDropdownChange('noPax', e.target.checked)
                            }}
                        />
                        <span className="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-1/3 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100"></span>
                        <span className="ml-3 text-sm font-semibold uppercase">
                            Trips with Zero Pax
                        </span>
                    </Label>
                </div>
            </div>
            <div className="mr-2">
                <VesselDropdown
                    isClearable
                    isMulti
                    onChange={(data: any) =>
                        handleDropdownChange('vessels', data)
                    }
                />
            </div>
            {/* <div className="mr-2">
                <TripScheduleServiceDropdown
                    isClearable
                    isMulti
                    withNonScheduledOption
                    onChange={(data: any) =>
                        handleDropdownChange('tripScheduleServices', data)
                    }
                />
            </div> */}
        </div>
    )
}

const AllocatedTasksFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    return (
        <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />

                <MaintenanceStatusDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('status', data)
                    }
                />

                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
            </div>
        </div>
    )
}
